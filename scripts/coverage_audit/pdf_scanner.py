#!/usr/bin/env python3
"""
PDF Scanner for Legal Deadline Discovery

This script scans PDF legal documents to identify all deadline-bearing provisions
and creates a canonical checklist for coverage auditing.

Usage:
    python scripts/coverage_audit/pdf_scanner.py --pdf rules/Texas/sources/codeofcriminalprocedure.pdf --jurisdiction texas --practice-area criminal
    python scripts/coverage_audit/pdf_scanner.py --scan-all
"""

import argparse
import re
import json
import yaml
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
import PyPDF2
from collections import defaultdict, Counter

@dataclass
class DeadlineMatch:
    text: str
    page_number: int
    context: str  # Surrounding text for context
    deadline_pattern: str  # The specific pattern that matched
    article_section: Optional[str] = None  # Article/Section reference if found
    confidence: float = 0.0  # Confidence this is a real calendar deadline

@dataclass
class SourceScanResult:
    source_file: str
    jurisdiction: str
    practice_area: str
    total_pages: int
    total_matches: int
    calendar_deadlines: int
    administrative_deadlines: int
    false_positives: int
    matches: List[DeadlineMatch]

class PDFDeadlineScanner:
    def __init__(self):
        # Comprehensive deadline patterns
        self.deadline_patterns = [
            # Standard time periods
            r'\b(\d+)\s+(day|days|month|months|year|years|hour|hours|minute|minutes)\b',
            # Within X days/months
            r'\bwithin\s+(\d+)\s+(day|days|month|months|year|years|hour|hours)\b',
            # Not later than X days
            r'\bnot\s+later\s+than\s+(?:the\s+)?(\d+)(?:st|nd|rd|th)?\s+(day|days|month|months|year|years)\b',
            # Before/after X days
            r'\b(?:before|after)\s+(?:the\s+)?(\d+)(?:st|nd|rd|th)?\s+(day|days|month|months|year|years)\b',
            # At least X days
            r'\bat\s+least\s+(\d+)\s+(day|days|month|months|year|years|hour|hours)\b',
            # No more than X days
            r'\bno\s+more\s+than\s+(\d+)\s+(day|days|month|months|year|years|hour|hours)\b',
            # X business/working days
            r'\b(\d+)\s+(?:business|working|court)\s+(day|days)\b',
            # Specific day references (30th day, 10th day, etc.)
            r'\b(\d+)(?:st|nd|rd|th)\s+day\b',
            # Time periods with "from" or "after"
            r'\b(\d+)\s+(day|days|month|months|year|years)\s+(?:from|after|following)\b',
        ]
        
        # Article/Section patterns for legal documents
        self.article_patterns = [
            r'\bArticle\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
            r'\bArt\.\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
            r'\bSection\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
            r'\bSec\.\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
            r'\bRule\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
            r'\bChapter\s+(\d+(?:\.\d+)*(?:[A-Za-z])?)\b',
        ]
        
        # False positive indicators (administrative, not calendar deadlines)
        self.false_positive_indicators = [
            r'\bfine\s+not\s+to\s+exceed\b',
            r'\bconfinement.*not\s+to\s+exceed\b',
            r'\bpunishable\s+by\b',
            r'\bsentence.*not\s+to\s+exceed\b',
            r'\bimprisonment.*not\s+to\s+exceed\b',
            r'\bstatute\s+of\s+limitations\b',  # These are deadlines but different category
            r'\bretention.*period\b',
            r'\brecord.*retention\b',
            r'\barchive\b',
            r'\bstorage\b',
        ]
        
        # High confidence indicators (definitely calendar deadlines)
        self.high_confidence_indicators = [
            r'\bfile\b.*\bwithin\b',
            r'\bserve\b.*\bwithin\b',
            r'\bmust\s+be\s+filed\b',
            r'\bmust\s+be\s+served\b',
            r'\bdeadline\b',
            r'\btime\s+limit\b',
            r'\bexpir[es]\b',
            r'\bmust.*within\b',
            r'\bshall.*within\b',
            r'\brequired.*within\b',
            r'\bmust\s+occur\s+within\b',
            r'\bmust\s+be\s+completed\s+within\b',
        ]

    def extract_text_from_pdf(self, pdf_path: str) -> List[Tuple[int, str]]:
        """Extract text from PDF, returning list of (page_number, text) tuples"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                pages = []
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        text = page.extract_text()
                        if text.strip():  # Only include pages with text
                            pages.append((page_num, text))
                    except Exception as e:
                        print(f"⚠️  Error extracting page {page_num}: {e}")
                        continue
                
                return pages
        except Exception as e:
            print(f"❌ Error reading PDF {pdf_path}: {e}")
            return []

    def find_deadline_matches(self, text: str, page_number: int) -> List[DeadlineMatch]:
        """Find all deadline patterns in text"""
        matches = []
        
        # Split text into sentences for better context
        sentences = re.split(r'[.!?]+', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # Skip very short fragments
                continue
                
            # Check each deadline pattern
            for pattern in self.deadline_patterns:
                for match in re.finditer(pattern, sentence, re.IGNORECASE):
                    # Extract context (the full sentence)
                    context = sentence[:200] + "..." if len(sentence) > 200 else sentence
                    
                    # Find article/section reference
                    article_section = self._find_article_reference(sentence)
                    
                    # Calculate confidence score
                    confidence = self._calculate_confidence(sentence)
                    
                    deadline_match = DeadlineMatch(
                        text=match.group(0),
                        page_number=page_number,
                        context=context,
                        deadline_pattern=pattern,
                        article_section=article_section,
                        confidence=confidence
                    )
                    
                    matches.append(deadline_match)
        
        return matches

    def _find_article_reference(self, text: str) -> Optional[str]:
        """Find article/section reference in text"""
        for pattern in self.article_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        return None

    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence that this is a real calendar deadline"""
        confidence = 0.5  # Base confidence
        
        # Check for false positive indicators
        for indicator in self.false_positive_indicators:
            if re.search(indicator, text, re.IGNORECASE):
                confidence -= 0.3
        
        # Check for high confidence indicators
        for indicator in self.high_confidence_indicators:
            if re.search(indicator, text, re.IGNORECASE):
                confidence += 0.3
        
        # Additional heuristics
        if re.search(r'\bcourt\b', text, re.IGNORECASE):
            confidence += 0.1
        if re.search(r'\bmust\b|\bshall\b|\brequired\b', text, re.IGNORECASE):
            confidence += 0.1
        if re.search(r'\bfiling\b|\bservice\b|\bnotice\b', text, re.IGNORECASE):
            confidence += 0.2
        
        return max(0.0, min(1.0, confidence))  # Clamp between 0 and 1

    def scan_pdf(self, pdf_path: str, jurisdiction: str, practice_area: str) -> SourceScanResult:
        """Scan a single PDF for deadline patterns"""
        print(f"🔍 Scanning {pdf_path}...")
        
        pages = self.extract_text_from_pdf(pdf_path)
        if not pages:
            print(f"❌ No text extracted from {pdf_path}")
            return SourceScanResult(
                source_file=pdf_path,
                jurisdiction=jurisdiction,
                practice_area=practice_area,
                total_pages=0,
                total_matches=0,
                calendar_deadlines=0,
                administrative_deadlines=0,
                false_positives=0,
                matches=[]
            )
        
        all_matches = []
        for page_num, text in pages:
            matches = self.find_deadline_matches(text, page_num)
            all_matches.extend(matches)
        
        # Categorize matches by confidence
        calendar_deadlines = len([m for m in all_matches if m.confidence >= 0.7])
        administrative_deadlines = len([m for m in all_matches if 0.3 <= m.confidence < 0.7])
        false_positives = len([m for m in all_matches if m.confidence < 0.3])
        
        result = SourceScanResult(
            source_file=pdf_path,
            jurisdiction=jurisdiction,
            practice_area=practice_area,
            total_pages=len(pages),
            total_matches=len(all_matches),
            calendar_deadlines=calendar_deadlines,
            administrative_deadlines=administrative_deadlines,
            false_positives=false_positives,
            matches=all_matches
        )
        
        print(f"  📄 {len(pages)} pages scanned")
        print(f"  🎯 {len(all_matches)} total matches found")
        print(f"  ✅ {calendar_deadlines} high-confidence calendar deadlines")
        print(f"  ⚠️  {administrative_deadlines} medium-confidence matches")
        print(f"  ❌ {false_positives} likely false positives")
        
        return result

    def scan_all_sources(self) -> List[SourceScanResult]:
        """Scan all available PDF sources"""
        sources = [
            # Texas sources
            ("rules/Texas/sources/codeofcriminalprocedure.pdf", "texas", "criminal"),
            ("rules/Texas/sources/CIVILPRACTICEANDREMEDIESCODE.pdf", "texas", "civil"),
            ("rules/Texas/sources/FAMILYCODE.pdf", "texas", "family"),
            # Florida sources
            ("rules/florida/source_pdfs/florida_civil_procedure_2025.pdf", "florida", "civil"),
            ("rules/florida/source_pdfs/florida_criminal_procedure_2025.pdf", "florida", "criminal"),
            ("rules/florida/source_pdfs/florida_family_law_2024.pdf", "florida", "family"),
        ]
        
        results = []
        for pdf_path, jurisdiction, practice_area in sources:
            if os.path.exists(pdf_path):
                result = self.scan_pdf(pdf_path, jurisdiction, practice_area)
                results.append(result)
            else:
                print(f"⚠️  Source not found: {pdf_path}")
        
        return results

    def generate_canonical_checklist(self, scan_results: List[SourceScanResult]) -> Dict:
        """Generate canonical checklist from scan results"""
        checklist = {
            "metadata": {
                "generated_date": __import__('datetime').datetime.now().isoformat(),
                "total_sources_scanned": len(scan_results),
                "scanning_methodology": "Automated regex pattern matching with confidence scoring"
            },
            "sources": {}
        }
        
        for result in scan_results:
            source_key = f"{result.jurisdiction}_{result.practice_area}"
            
            # Group matches by article/section
            by_article = defaultdict(list)
            for match in result.matches:
                if match.confidence >= 0.7:  # Only high-confidence matches
                    key = match.article_section or "unspecified"
                    by_article[key].append(match)
            
            checklist["sources"][source_key] = {
                "source_file": result.source_file,
                "jurisdiction": result.jurisdiction,
                "practice_area": result.practice_area,
                "scan_stats": {
                    "total_pages": result.total_pages,
                    "total_matches": result.total_matches,
                    "calendar_deadlines": result.calendar_deadlines,
                    "estimated_true_deadlines": result.calendar_deadlines,  # Conservative estimate
                },
                "deadline_distribution": dict(Counter([m.article_section or "unspecified" 
                                                     for m in result.matches if m.confidence >= 0.7])),
                "sample_matches": [
                    {
                        "text": match.text,
                        "context": match.context,
                        "article_section": match.article_section,
                        "page": match.page_number,
                        "confidence": round(match.confidence, 2)
                    }
                    for match in sorted(result.matches, key=lambda x: x.confidence, reverse=True)[:10]
                    if match.confidence >= 0.7
                ]
            }
        
        return checklist

def main():
    parser = argparse.ArgumentParser(description="PDF Scanner for Legal Deadline Discovery")
    parser.add_argument("--pdf", help="Path to PDF file to scan")
    parser.add_argument("--jurisdiction", help="Jurisdiction (e.g., texas, florida)")
    parser.add_argument("--practice-area", help="Practice area (e.g., criminal, civil, family)")
    parser.add_argument("--scan-all", action="store_true", help="Scan all available sources")
    parser.add_argument("--output", default="scripts/coverage_audit/canonical_checklist.json",
                       help="Output file for canonical checklist")
    
    args = parser.parse_args()
    
    scanner = PDFDeadlineScanner()
    
    if args.scan_all:
        print("🔍 Scanning all available PDF sources...")
        results = scanner.scan_all_sources()
    elif args.pdf and args.jurisdiction and args.practice_area:
        print(f"🔍 Scanning single PDF: {args.pdf}")
        result = scanner.scan_pdf(args.pdf, args.jurisdiction, args.practice_area)
        results = [result]
    else:
        print("❌ Either --scan-all or --pdf + --jurisdiction + --practice-area required")
        sys.exit(1)
    
    if not results:
        print("❌ No results to process")
        sys.exit(1)
    
    # Generate canonical checklist
    checklist = scanner.generate_canonical_checklist(results)
    
    # Save results
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    with open(args.output, 'w') as f:
        json.dump(checklist, f, indent=2)
    
    print(f"\n✅ Canonical checklist saved to {args.output}")
    
    # Print summary
    total_deadlines = sum(r.calendar_deadlines for r in results)
    print(f"\n📊 SUMMARY:")
    print(f"Sources scanned: {len(results)}")
    print(f"Total high-confidence calendar deadlines: {total_deadlines}")
    
    for result in results:
        print(f"  {result.jurisdiction.upper()} {result.practice_area.upper()}: {result.calendar_deadlines} deadlines")

if __name__ == "__main__":
    main()
